import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Req
} from '@nestjs/common';
import { DroneAuthorizationService } from './droneAuthorization.service';
import { DroneAuthorization } from './droneAuthorization.interface';

@Controller('api/drone-authorizations')
export class DroneAuthorizationController {
  constructor(private readonly droneAuthorizationService: DroneAuthorizationService) {}

  @Get()
  async findAll(
    @Query('skip') skip?: number,
    @Query('limit') limit?: number
  ): Promise<{ authorizations: DroneAuthorization[], total: number }> {
    return this.droneAuthorizationService.findAll(skip, limit);
  }

  @Get('drone/:droneId')
  async findByDroneId(@Param('droneId') droneId: string): Promise<DroneAuthorization[]> {
    return this.droneAuthorizationService.findByDroneId(droneId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<DroneAuthorization> {
    return this.droneAuthorizationService.findOne(id);
  }

  @Post()
  async create(@Req() req: Request, @Body() authorizationData: Partial<DroneAuthorization>): Promise<DroneAuthorization> {
    const user = req['user'];

    // Set authorized_by if not provided
    if (!authorizationData.authorized_by) {
      authorizationData.authorized_by = user.name || user._id;
    }

    return this.droneAuthorizationService.create(authorizationData, user._id, user.org_id);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() authorizationData: Partial<DroneAuthorization>,
    @Req() req: Request
  ): Promise<DroneAuthorization> {
    const user = req['user'];
    return this.droneAuthorizationService.update(id, authorizationData, user._id);
  }

  @Delete(':id')
  async delete(@Param('id') id: string, @Req() req: Request): Promise<{ message: string }> {
    const user = req['user'];
    return this.droneAuthorizationService.delete(id, user._id, user.org_id);
  }

  @Post('cache/clear')
  async clearAllCache(@Req() req: Request): Promise<{ success: boolean; message: string; clearedKeys?: string[] }> {
    // Optional: Add authorization check here if needed
    // const user = req['user'];
    // if (!user.isAdmin) throw new UnauthorizedException('Admin access required');

    return this.droneAuthorizationService.clearAllCache();
  }

  @Post('cache/clear/drone/:droneId')
  async clearDroneCache(
    @Param('droneId') droneId: string,
    @Req() req: Request
  ): Promise<{ success: boolean; message: string }> {
    const user = req['user'];
    return this.droneAuthorizationService.clearDroneCache(droneId, user.org_id);
  }

  @Get('cache/stats')
  async getCacheStats(@Req() req: Request): Promise<{ success: boolean; stats?: any; message: string }> {
    // Optional: Add authorization check here if needed
    // const user = req['user'];
    // if (!user.isAdmin) throw new UnauthorizedException('Admin access required');

    return this.droneAuthorizationService.getCacheStats();
  }
}
