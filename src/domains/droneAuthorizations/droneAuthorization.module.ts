import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DroneAuthorizationController } from './droneAuthorization.controller';
import { DroneAuthorizationService } from './droneAuthorization.service';
import DroneAuthorizationSchema from './droneAuthorization.schema';
import Constants from 'src/common/constants';
import { AppSyncService } from 'src/utils/appsync.service';
import { OrganizationModule } from '../organizations/organization.module';
import { LoggingModule } from '../logging/logging.module';
import { DroneModule } from '../drones/drone.module';
import { CacheManagementService } from '../../utils/cache-management.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Constants.droneAuthorizations, schema: DroneAuthorizationSchema }]),
    OrganizationModule,
    LoggingModule,
    forwardRef(() => DroneModule),
  ],
  controllers: [DroneAuthorizationController],
  providers: [DroneAuthorizationService, AppSyncService, CacheManagementService],
  exports: [DroneAuthorizationService, AppSyncService, CacheManagementService],
})
export class DroneAuthorizationModule {}
