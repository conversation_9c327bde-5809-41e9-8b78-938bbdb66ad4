import { Injectable, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { DroneAuthorization } from './droneAuthorization.interface';
import Constants from 'src/common/constants';
import { AppSyncService } from 'src/utils/appsync.service';
import { OrganizationService } from '../organizations/organization.service';
import { SystemNotification } from '../systemNotifications/systemNotification.interface';
import { CacheKeyPatterns } from '../../utils/cache.utils';
import { LoggingService } from '../logging/logging.service';
import { LogActionEnum } from 'src/common/enums/LogActionEnum';
import { LogEntityEnum } from 'src/common/enums/LogEntityEnum';
import { DroneService } from '../drones/drone.service';
import { CacheManagementService } from '../../utils/cache-management.service';

@Injectable()
export class DroneAuthorizationService {
  // Cache TTL constants (in milliseconds)
  private static readonly CACHE_TTL_AUTHORIZATION = 180000; // 3 minutes - shorter TTL for authorization data
  constructor(
    @InjectModel(Constants.droneAuthorizations) private droneAuthorizationModel: Model<DroneAuthorization>,
    private readonly appSyncService: AppSyncService,
    private readonly organizationService: OrganizationService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly loggingService: LoggingService,
    @Inject(forwardRef(() => DroneService))
    private readonly droneService: DroneService,
    private readonly cacheManagementService: CacheManagementService
  ) {}

  private async invalidateAuthorizationCache(
    authorizationId: string,
    droneId: string,
    orgId?: string,
    droneData?: any
  ): Promise<void> {
    try {
      const cacheKeysToDelete = [
        CacheKeyPatterns.DRONE_AUTH_BY_ID(authorizationId),
        CacheKeyPatterns.DRONE_AUTH_BY_DRONE_ID(droneId),
        CacheKeyPatterns.DRONE_AUTH_LATEST(droneId),
        CacheKeyPatterns.DRONE_AUTH_BY_ORG_ID_AND_DEVICE_ID(orgId, droneData?.device_id)
      ];

      // Add the legacy authorized drone cache key if orgId and device_id are available
      if (orgId && droneData?.device_id) {
        const legacyCacheKey = `authorized_drone:org_id:${orgId}:device_id:${droneData.device_id}`;
        cacheKeysToDelete.push(legacyCacheKey);
      }

      // Invalidate all cache keys in parallel
      await Promise.all(cacheKeysToDelete.map(key => this.cacheManager.del(key)));
    } catch (error) {
      // Log cache invalidation errors but don't fail the operation
      console.warn(`Failed to invalidate authorization cache: ${error.message}`);
    }
  }

  async findAll(skip: number = 0, limit: number = 10): Promise<{ authorizations: DroneAuthorization[], total: number }> {
    const [authorizations, total] = await Promise.all([
      this.droneAuthorizationModel
        .find({ isDeleted: false })
        .skip(skip)
        .limit(limit)
        .sort({ issued_at: -1 })
        .exec(),
      this.droneAuthorizationModel.countDocuments({ isDeleted: false }).exec(),
    ]);

    return { authorizations, total };
  }

  async findByDroneId(droneId: string): Promise<DroneAuthorization[]> {
    const cacheKey = CacheKeyPatterns.DRONE_AUTH_BY_DRONE_ID(droneId);

    // Try to get from cache first
    const cachedAuthorizations = await this.cacheManager.get<DroneAuthorization[]>(cacheKey);
    if (cachedAuthorizations) {
      return cachedAuthorizations;
    }

    // If not in cache, query database
    const authorizations = await this.droneAuthorizationModel
      .find({ drone_id: new Types.ObjectId(droneId), isDeleted: false })
      .sort({ issued_at: -1 })
      .exec();

    // Store in cache with shorter TTL for authorization data
    if (authorizations) {
      await this.cacheManager.set(cacheKey, authorizations, DroneAuthorizationService.CACHE_TTL_AUTHORIZATION);
    }

    return authorizations;
  }

  async findOne(id: string): Promise<DroneAuthorization> {
    const cacheKey = CacheKeyPatterns.DRONE_AUTH_BY_ID(id);

    // Try to get from cache first
    const cachedAuthorization = await this.cacheManager.get<DroneAuthorization>(cacheKey);
    if (cachedAuthorization) {
      return cachedAuthorization;
    }

    // If not in cache, query database
    const authorization = await this.droneAuthorizationModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!authorization) {
      throw new NotFoundException(`Authorization with ID ${id} not found`);
    }

    // Store in cache with shorter TTL for authorization data
    await this.cacheManager.set(cacheKey, authorization, DroneAuthorizationService.CACHE_TTL_AUTHORIZATION);

    return authorization;
  }

  async create(authorizationData: Partial<DroneAuthorization>, userId: string, orgId?: string): Promise<DroneAuthorization> {
    // Create new authorization
    const newAuthorization = new this.droneAuthorizationModel({
      ...authorizationData,
      org_id: orgId ? orgId : undefined,
      createdBy: new Types.ObjectId(userId),
      updatedBy: new Types.ObjectId(userId),
    });

    const savedAuthorization = await newAuthorization.save();

    // Log the creation
    await this.loggingService.createLog(
      LogActionEnum.CREATE,
      LogEntityEnum.DRONE_AUTHORIZATION,
      savedAuthorization._id.toString(),
      userId,
      {
        authorized_by: savedAuthorization.authorized_by,
        notes: savedAuthorization.notes,
        drone_id: savedAuthorization.drone_id.toString(),
      },
      orgId
    );

    // Invalidate cache for this drone
    if (savedAuthorization.drone_id) {
      const droneId = savedAuthorization.drone_id.toString();
      await this.invalidateAuthorizationCache(
        savedAuthorization._id.toString(),
        droneId,
        orgId
      );
    }

    return savedAuthorization;
  }

  async publishNotification(notification: SystemNotification) {
    const org = await this.organizationService.findById(notification.org_id.toString());
    if (org) {
      this.appSyncService.publishMessage(org.auth0_id, notification);
    }
  }

  async update(id: string, authorizationData: Partial<DroneAuthorization>, userId: string): Promise<DroneAuthorization> {
    // Check if authorization exists
    const authorization = await this.droneAuthorizationModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!authorization) {
      throw new NotFoundException(`Authorization with ID ${id} not found`);
    }

    // Update authorization
    const updatedAuthorization = await this.droneAuthorizationModel.findByIdAndUpdate(
      id,
      {
        ...authorizationData,
        updatedBy: new Types.ObjectId(userId),
        updatedAt: new Date(),
      },
      { new: true }
    ).exec();

    // Invalidate cache for this authorization and drone
    const droneId = authorization.drone_id.toString();
    await this.invalidateAuthorizationCache(id, droneId);

    return updatedAuthorization;
  }

  async delete(id: string, userId: string, orgId?: string): Promise<{ message: string }> {
    const authorization = await this.droneAuthorizationModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!authorization) {
      throw new NotFoundException(`Authorization with ID ${id} not found`);
    }

    // Get drone data for additional cache invalidation (legacy cache keys)
    const droneId = authorization.drone_id.toString();
    let droneData = null;

    // Only fetch drone data if we need it for legacy cache invalidation
    if (orgId) {
      try {
        droneData = await this.droneService.findOne(droneId);
      } catch (error) {
        // If drone data fetch fails, continue without legacy cache invalidation
        console.warn(`Failed to fetch drone data for cache invalidation: ${error.message}`);
      }
    }

    // Soft delete
    await this.droneAuthorizationModel.findByIdAndUpdate(
      id,
      {
        isDeleted: true,
        isActive: false,
        deletedAt: new Date(),
        deletedBy: new Types.ObjectId(userId),
      }
    ).exec();

    // Log the deletion
    await this.loggingService.createLog(
      LogActionEnum.DELETE,
      LogEntityEnum.DRONE_AUTHORIZATION,
      id,
      userId,
      {
        authorized_by: authorization.authorized_by,
        notes: authorization.notes,
        drone_id: droneId,
      },
      orgId
    );

    // Invalidate all cache keys using the helper method
    await this.invalidateAuthorizationCache(id, droneId, orgId, droneData);

    return { message: 'Authorization deleted successfully' };
  }

  async getLatestAuthorizationForDrone(droneId: string): Promise<DroneAuthorization | null> {
    const cacheKey = CacheKeyPatterns.DRONE_AUTH_LATEST(droneId);

    // Try to get from cache first
    const cachedAuthorization = await this.cacheManager.get<DroneAuthorization>(cacheKey);
    if (cachedAuthorization) {
      return cachedAuthorization;
    }

    // If not in cache, query database
    const authorization = await this.droneAuthorizationModel
      .findOne({ drone_id: new Types.ObjectId(droneId), isDeleted: false })
      .sort({ issued_at: -1 })
      .exec();

    // Store in cache - critical for authorization checks
    if (authorization) {
      await this.cacheManager.set(cacheKey, authorization, DroneAuthorizationService.CACHE_TTL_AUTHORIZATION);
    }

    return authorization;
  }

  /**
   * Clear all cache entries related to drone authorizations
   * This includes all drone authorization cache patterns and legacy cache keys
   * @returns Object with success status and cleared cache count
   */
  async clearAllCache(): Promise<{ success: boolean; message: string; clearedKeys?: string[] }> {
    try {
      // Get all cache keys that match our patterns
      const environment = process.env.ENV;
      const envPrefix = environment === 'prod' ? '' : `${environment || 'dev'}:`;

      // Define specific cache keys to clear
      const specificKeysToTry = [
        'auth0_management_token',             // Auth0 management token cache
      ];

      // Define patterns for clearing
      const cachePatterns = [
        `${envPrefix}drone:auth:*`,          // All drone authorization cache keys
        `${envPrefix}user:sub:*`,            // User cache keys
        `${envPrefix}servicezone:*`,         // Service zone cache keys
        `${envPrefix}org:auth0:*`,           // Organization cache keys
        `authorized_drone:*`,                // Legacy authorized drone cache keys
        `cache:GET:*`,                       // HTTP cache interceptor keys
      ];

      // Clear specific keys first
      const specificResult = await this.cacheManagementService.clearSpecificKeys(specificKeysToTry);

      // Clear pattern-based keys
      const patternResult = await this.cacheManagementService.clearCacheByPatterns(cachePatterns);

      // Combine results
      const allClearedKeys = [...specificResult.clearedKeys, ...patternResult.clearedKeys];

      return {
        success: specificResult.success && patternResult.success,
        message: `Successfully cleared ${allClearedKeys.length} cache entries`,
        clearedKeys: allClearedKeys.length > 0 ? allClearedKeys : undefined
      };

    } catch (error) {
      console.error('Failed to clear cache:', error);
      return {
        success: false,
        message: `Failed to clear cache: ${error.message}`
      };
    }
  }

  /**
   * Clear cache entries for a specific drone
   * @param droneId - The drone ID to clear cache for
   * @param orgId - Optional organization ID for legacy cache keys
   * @returns Object with success status and message
   */
  async clearDroneCache(droneId: string, orgId?: string): Promise<{ success: boolean; message: string }> {
    try {
      const cacheKeysToDelete = [
        CacheKeyPatterns.DRONE_AUTH_BY_DRONE_ID(droneId),
        CacheKeyPatterns.DRONE_AUTH_LATEST(droneId),
      ];

      // If orgId is provided, try to clear legacy cache keys
      if (orgId) {
        try {
          const droneData = await this.droneService.findOne(droneId);
          if (droneData?.device_id) {
            const legacyCacheKey = `authorized_drone:org_id:${orgId}:device_id:${droneData.device_id}`;
            cacheKeysToDelete.push(legacyCacheKey);
          }
        } catch (error) {
          console.warn(`Could not fetch drone data for cache clearing: ${error.message}`);
        }
      }

      // Clear all cache keys in parallel
      await Promise.all(cacheKeysToDelete.map(key => this.cacheManager.del(key)));

      return {
        success: true,
        message: `Successfully cleared cache for drone ${droneId}`
      };

    } catch (error) {
      console.error(`Failed to clear drone cache for ${droneId}:`, error);
      return {
        success: false,
        message: `Failed to clear drone cache: ${error.message}`
      };
    }
  }

  /**
   * Get cache statistics
   * @returns Object with cache statistics
   */
  async getCacheStats(): Promise<{ success: boolean; stats?: any; message: string }> {
    return this.cacheManagementService.getCacheStats();
  }
}
