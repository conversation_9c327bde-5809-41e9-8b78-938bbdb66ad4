import { Injectable, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { DroneAuthorization } from './droneAuthorization.interface';
import Constants from 'src/common/constants';
import { AppSyncService } from 'src/utils/appsync.service';
import { OrganizationService } from '../organizations/organization.service';
import { SystemNotification } from '../systemNotifications/systemNotification.interface';
import { CacheKeyPatterns } from '../../utils/cache.utils';
import { LoggingService } from '../logging/logging.service';
import { LogActionEnum } from 'src/common/enums/LogActionEnum';
import { LogEntityEnum } from 'src/common/enums/LogEntityEnum';
import { DroneService } from '../drones/drone.service';

@Injectable()
export class DroneAuthorizationService {
  constructor(
    @InjectModel(Constants.droneAuthorizations) private droneAuthorizationModel: Model<DroneAuthorization>,
    private readonly appSyncService: AppSyncService,
    private readonly organizationService: OrganizationService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly loggingService: LoggingService,
    @Inject(forwardRef(() => DroneService))
    private readonly droneService: DroneService
  ) {}

  async findAll(skip: number = 0, limit: number = 10): Promise<{ authorizations: DroneAuthorization[], total: number }> {
    const [authorizations, total] = await Promise.all([
      this.droneAuthorizationModel
        .find({ isDeleted: false })
        .skip(skip)
        .limit(limit)
        .sort({ issued_at: -1 })
        .exec(),
      this.droneAuthorizationModel.countDocuments({ isDeleted: false }).exec(),
    ]);

    return { authorizations, total };
  }

  async findByDroneId(droneId: string): Promise<DroneAuthorization[]> {
    const cacheKey = CacheKeyPatterns.DRONE_AUTH_BY_DRONE_ID(droneId);

    // Try to get from cache first
    const cachedAuthorizations = await this.cacheManager.get<DroneAuthorization[]>(cacheKey);
    if (cachedAuthorizations) {
      return cachedAuthorizations;
    }

    // If not in cache, query database
    const authorizations = await this.droneAuthorizationModel
      .find({ drone_id: new Types.ObjectId(droneId), isDeleted: false })
      .sort({ issued_at: -1 })
      .exec();

    // Store in cache with 3 minute TTL (180 seconds) - shorter TTL for authorization data
    if (authorizations) {
      await this.cacheManager.set(cacheKey, authorizations, 180000); // 3 minutes in milliseconds
    }

    return authorizations;
  }

  async findOne(id: string): Promise<DroneAuthorization> {
    const cacheKey = CacheKeyPatterns.DRONE_AUTH_BY_ID(id);

    // Try to get from cache first
    const cachedAuthorization = await this.cacheManager.get<DroneAuthorization>(cacheKey);
    if (cachedAuthorization) {
      return cachedAuthorization;
    }

    // If not in cache, query database
    const authorization = await this.droneAuthorizationModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!authorization) {
      throw new NotFoundException(`Authorization with ID ${id} not found`);
    }

    // Store in cache with 3 minute TTL (180 seconds)
    await this.cacheManager.set(cacheKey, authorization, 180000); // 3 minutes in milliseconds

    return authorization;
  }

  async create(authorizationData: Partial<DroneAuthorization>, userId: string, orgId?: string): Promise<DroneAuthorization> {
    // Create new authorization
    const newAuthorization = new this.droneAuthorizationModel({
      ...authorizationData,
      org_id: orgId ? orgId : undefined,
      createdBy: new Types.ObjectId(userId),
      updatedBy: new Types.ObjectId(userId),
    });

    const savedAuthorization = await newAuthorization.save();

    // Log the creation
    await this.loggingService.createLog(
      LogActionEnum.CREATE,
      LogEntityEnum.DRONE_AUTHORIZATION,
      savedAuthorization._id.toString(),
      userId,
      {
        authorized_by: savedAuthorization.authorized_by,
        notes: savedAuthorization.notes,
        drone_id: savedAuthorization.drone_id.toString(),
      },
      orgId
    );

    // Invalidate cache for this drone
    if (savedAuthorization.drone_id) {
      const droneId = savedAuthorization.drone_id.toString();
      await Promise.all([
        this.cacheManager.del(CacheKeyPatterns.DRONE_AUTH_BY_DRONE_ID(droneId)),
        this.cacheManager.del(CacheKeyPatterns.DRONE_AUTH_LATEST(droneId)),
      ]);
    }

    return savedAuthorization;
  }

  async publishNotification(notification: SystemNotification) {
    const org = await this.organizationService.findById(notification.org_id.toString());
    if (org) {
      this.appSyncService.publishMessage(org.auth0_id, notification);
    }
  }

  async update(id: string, authorizationData: Partial<DroneAuthorization>, userId: string): Promise<DroneAuthorization> {
    // Check if authorization exists
    const authorization = await this.droneAuthorizationModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!authorization) {
      throw new NotFoundException(`Authorization with ID ${id} not found`);
    }

    // Update authorization
    const updatedAuthorization = await this.droneAuthorizationModel.findByIdAndUpdate(
      id,
      {
        ...authorizationData,
        updatedBy: new Types.ObjectId(userId),
        updatedAt: new Date(),
      },
      { new: true }
    ).exec();

    // Invalidate cache for this authorization and drone
    const droneId = authorization.drone_id.toString();
    await Promise.all([
      this.cacheManager.del(CacheKeyPatterns.DRONE_AUTH_BY_ID(id)),
      this.cacheManager.del(CacheKeyPatterns.DRONE_AUTH_BY_DRONE_ID(droneId)),
      this.cacheManager.del(CacheKeyPatterns.DRONE_AUTH_LATEST(droneId)),
    ]);

    return updatedAuthorization;
  }

  async delete(id: string, userId: string, orgId?: string): Promise<{ message: string }> {
    const authorization = await this.droneAuthorizationModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!authorization) {
      throw new NotFoundException(`Authorization with ID ${id} not found`);
    }

    // Soft delete
    await this.droneAuthorizationModel.findByIdAndUpdate(
      id,
      {
        isDeleted: true,
        isActive: false,
        deletedAt: new Date(),
        deletedBy: new Types.ObjectId(userId),
      }
    ).exec();

    // Log the deletion
    await this.loggingService.createLog(
      LogActionEnum.DELETE,
      LogEntityEnum.DRONE_AUTHORIZATION,
      id,
      userId,
      {
        authorized_by: authorization.authorized_by,
        notes: authorization.notes,
        drone_id: authorization.drone_id.toString(),
      },
      orgId
    );

    // Invalidate cache for this authorization and drone
    const droneId = authorization.drone_id.toString();
    await Promise.all([
      this.cacheManager.del(CacheKeyPatterns.DRONE_AUTH_BY_ID(id)),
      this.cacheManager.del(CacheKeyPatterns.DRONE_AUTH_BY_DRONE_ID(droneId)),
      this.cacheManager.del(CacheKeyPatterns.DRONE_AUTH_LATEST(droneId)),
    ]);

    return { message: 'Authorization deleted successfully' };
  }

  async getLatestAuthorizationForDrone(droneId: string): Promise<DroneAuthorization | null> {
    const cacheKey = CacheKeyPatterns.DRONE_AUTH_LATEST(droneId);

    // Try to get from cache first
    const cachedAuthorization = await this.cacheManager.get<DroneAuthorization>(cacheKey);
    if (cachedAuthorization) {
      return cachedAuthorization;
    }

    // If not in cache, query database
    const authorization = await this.droneAuthorizationModel
      .findOne({ drone_id: new Types.ObjectId(droneId), isDeleted: false })
      .sort({ issued_at: -1 })
      .exec();

    // Store in cache with 3 minute TTL (180 seconds) - critical for authorization checks
    if (authorization) {
      await this.cacheManager.set(cacheKey, authorization, 180000); // 3 minutes in milliseconds
    }

    return authorization;
  }
}
