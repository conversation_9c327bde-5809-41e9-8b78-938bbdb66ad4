import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class AdminService {
  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  async clearAllCache(): Promise<{ success: boolean; message: string; clearedKeys?: string[] }> {
    try {
      // Get all cache keys that match our patterns
      const environment = process.env.ENV;
      const envPrefix = environment === 'prod' ? '' : `${environment || 'dev'}:`;
      const store = this.cacheManager.stores[0];
      for await (const [key, value] of store.iterator({})) {
        console.log(key, value);
      }
    } catch (error) {
      console.error('Failed to clear cache:', error);
      return {
        success: false,
        message: `Failed to clear cache: ${error.message}`
      };
    }
  }
}