import { Document, Types } from 'mongoose';
import { NotificationTypeEnum } from 'src/common/enums/NotifocationTypeEnum';

export interface Notification extends Document {
  _id: string;
  event_id: string;
  created_at: Date;
  org_id: Types.ObjectId,
  alertZone: Array<{ name: String; _id: String }>,
  timestamp: Date,
  seen: Boolean,
  type: NotificationTypeEnum,
  droneAuthId: string,
  event?: any
}
