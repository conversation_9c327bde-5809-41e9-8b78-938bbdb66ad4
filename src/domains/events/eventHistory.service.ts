import { Injectable, NotFoundException, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { EventHistory } from './eventHistory.interface';
import Constants from 'src/common/constants';
import { CacheKeyPatterns } from '../../utils/cache.utils';

@Injectable()
export class EventHistoryService {
  // Cache TTL constants (in milliseconds)
  private static readonly CACHE_TTL_EVENT_HISTORY = 180000; // 3 minutes - shorter for real-time data

  constructor(
    @InjectModel(Constants.event_history) private eventHistoryModel: Model<EventHistory>,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  async findAll(skip: number = 0, limit: number = 10, filters?: any): Promise<{ eventHistory: EventHistory[], total: number }> {
    const query = { ...filters };
    
    const [eventHistory, total] = await Promise.all([
      this.eventHistoryModel
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ TIME_STAMP: -1 })
        .exec(),
      this.eventHistoryModel.countDocuments(query).exec(),
    ]);

    return { eventHistory, total };
  }

  async findByEventId(eventId: string, limit?: number): Promise<EventHistory[]> {
    const cacheKey = CacheKeyPatterns.EVENT_HISTORY_BY_EVENT_ID(eventId, limit);

    // Try to get from cache first
    const cachedEventHistory = await this.cacheManager.get<EventHistory[]>(cacheKey);
    if (cachedEventHistory) {
      return cachedEventHistory;
    }

    // If not in cache, query database
    let query = this.eventHistoryModel
      .find({ EVENT_ID: eventId })
      .sort({ TIME_STAMP: -1 });

    if (limit && limit > 0) {
      query = query.limit(limit);
    }

    const eventHistory = await query.exec();

    // Store in cache
    if (eventHistory) {
      await this.cacheManager.set(cacheKey, eventHistory, EventHistoryService.CACHE_TTL_EVENT_HISTORY);
    }

    return eventHistory;
  }

  async findByDeviceId(deviceId: string, limit?: number): Promise<EventHistory[]> {
    const cacheKey = CacheKeyPatterns.EVENT_HISTORY_BY_DEVICE_ID(deviceId, limit);

    // Try to get from cache first
    const cachedEventHistory = await this.cacheManager.get<EventHistory[]>(cacheKey);
    if (cachedEventHistory) {
      return cachedEventHistory;
    }

    // If not in cache, query database
    let query = this.eventHistoryModel
      .find({ DEVICE_ID: deviceId })
      .sort({ TIME_STAMP: -1 });

    if (limit && limit > 0) {
      query = query.limit(limit);
    }

    const eventHistory = await query.exec();

    // Store in cache
    if (eventHistory) {
      await this.cacheManager.set(cacheKey, eventHistory, EventHistoryService.CACHE_TTL_EVENT_HISTORY);
    }

    return eventHistory;
  }

  async findByDateRange(startDate: Date, endDate: Date, filters?: any): Promise<EventHistory[]> {
    const query = {
      TIME_STAMP: {
        $gte: startDate,
        $lte: endDate,
      },
      ...filters,
    };

    return this.eventHistoryModel
      .find(query)
      .sort({ TIME_STAMP: -1 })
      .exec();
  }

  async findByLocation(lat: number, lon: number, radiusKm: number = 1): Promise<EventHistory[]> {
    // Convert radius from kilometers to radians (Earth radius ≈ 6371 km)
    const radiusRadians = radiusKm / 6371;

    return this.eventHistoryModel
      .find({
        LAT: {
          $gte: lat - (radiusRadians * 180 / Math.PI),
          $lte: lat + (radiusRadians * 180 / Math.PI)
        },
        LON: {
          $gte: lon - (radiusRadians * 180 / Math.PI),
          $lte: lon + (radiusRadians * 180 / Math.PI)
        }
      })
      .sort({ TIME_STAMP: -1 })
      .exec();
  }

  async findOne(id: string): Promise<EventHistory> {
    const eventHistory = await this.eventHistoryModel.findById(id).exec();

    if (!eventHistory) {
      throw new NotFoundException(`Event history with ID ${id} not found`);
    }

    return eventHistory;
  }

  async create(eventHistoryData: Partial<EventHistory>): Promise<EventHistory> {
    const newEventHistory = new this.eventHistoryModel(eventHistoryData);
    const savedEventHistory = await newEventHistory.save();

    // Invalidate related cache entries
    await this.invalidateEventHistoryCache(
      savedEventHistory.EVENT_ID, 
      savedEventHistory.DEVICE_ID
    );

    return savedEventHistory;
  }

  async createMany(eventHistoryData: Partial<EventHistory>[]): Promise<EventHistory[]> {
    const savedEventHistory = await this.eventHistoryModel.insertMany(eventHistoryData);

    // Invalidate cache for all affected events and devices
    const eventIds = [...new Set(savedEventHistory.map(eh => eh.EVENT_ID))];
    const deviceIds = [...new Set(savedEventHistory.map(eh => eh.DEVICE_ID))];

    await Promise.all([
      ...eventIds.map(eventId => this.invalidateEventHistoryCacheByEventId(eventId)),
      ...deviceIds.map(deviceId => this.invalidateEventHistoryCacheByDeviceId(deviceId))
    ]);

    return savedEventHistory;
  }

  async update(id: string, eventHistoryData: Partial<EventHistory>): Promise<EventHistory> {
    const eventHistory = await this.eventHistoryModel.findById(id).exec();

    if (!eventHistory) {
      throw new NotFoundException(`Event history with ID ${id} not found`);
    }

    const updatedEventHistory = await this.eventHistoryModel.findByIdAndUpdate(
      id,
      eventHistoryData,
      { new: true }
    ).exec();

    // Invalidate cache
    await this.invalidateEventHistoryCache(
      eventHistory.EVENT_ID, 
      eventHistory.DEVICE_ID
    );

    return updatedEventHistory;
  }

  async delete(id: string): Promise<{ message: string }> {
    const eventHistory = await this.eventHistoryModel.findById(id).exec();

    if (!eventHistory) {
      throw new NotFoundException(`Event history with ID ${id} not found`);
    }

    await this.eventHistoryModel.findByIdAndDelete(id).exec();

    // Invalidate cache
    await this.invalidateEventHistoryCache(
      eventHistory.EVENT_ID, 
      eventHistory.DEVICE_ID
    );

    return { message: 'Event history deleted successfully' };
  }

  async getLatestByEventId(eventId: string): Promise<EventHistory | null> {
    const cacheKey = CacheKeyPatterns.EVENT_HISTORY_LATEST_BY_EVENT_ID(eventId);

    // Try to get from cache first
    const cachedEventHistory = await this.cacheManager.get<EventHistory>(cacheKey);
    if (cachedEventHistory) {
      return cachedEventHistory;
    }

    // If not in cache, query database
    const eventHistory = await this.eventHistoryModel
      .findOne({ EVENT_ID: eventId })
      .sort({ TIME_STAMP: -1 })
      .exec();

    // Store in cache
    if (eventHistory) {
      await this.cacheManager.set(cacheKey, eventHistory, EventHistoryService.CACHE_TTL_EVENT_HISTORY);
    }

    return eventHistory;
  }

  async getEventHistoryStats(eventId?: string, deviceId?: string): Promise<any> {
    const matchQuery: any = {};
    if (eventId) matchQuery.EVENT_ID = eventId;
    if (deviceId) matchQuery.DEVICE_ID = deviceId;

    const stats = await this.eventHistoryModel.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: null,
          totalRecords: { $sum: 1 },
          avgSpeed: { $avg: '$SPEED' },
          avgAltitude: { $avg: '$ALTITUDE' },
          minTimestamp: { $min: '$TIME_STAMP' },
          maxTimestamp: { $max: '$TIME_STAMP' },
          uniqueEvents: { $addToSet: '$EVENT_ID' },
          uniqueDevices: { $addToSet: '$DEVICE_ID' }
        }
      },
      {
        $project: {
          _id: 0,
          totalRecords: 1,
          avgSpeed: 1,
          avgAltitude: 1,
          minTimestamp: 1,
          maxTimestamp: 1,
          uniqueEventCount: { $size: '$uniqueEvents' },
          uniqueDeviceCount: { $size: '$uniqueDevices' }
        }
      }
    ]).exec();

    return stats[0] || {
      totalRecords: 0,
      avgSpeed: 0,
      avgAltitude: 0,
      minTimestamp: null,
      maxTimestamp: null,
      uniqueEventCount: 0,
      uniqueDeviceCount: 0
    };
  }

  /**
   * Helper method to invalidate cache entries related to event history
   */
  private async invalidateEventHistoryCache(eventId: string, deviceId: string): Promise<void> {
    try {
      await Promise.all([
        this.invalidateEventHistoryCacheByEventId(eventId),
        this.invalidateEventHistoryCacheByDeviceId(deviceId)
      ]);
    } catch (error) {
      console.warn(`Failed to invalidate event history cache: ${error.message}`);
    }
  }

  private async invalidateEventHistoryCacheByEventId(eventId: string): Promise<void> {
    const cacheKeysToDelete = [
      CacheKeyPatterns.EVENT_HISTORY_BY_EVENT_ID(eventId),
      CacheKeyPatterns.EVENT_HISTORY_BY_EVENT_ID(eventId, 10),
      CacheKeyPatterns.EVENT_HISTORY_BY_EVENT_ID(eventId, 50),
      CacheKeyPatterns.EVENT_HISTORY_BY_EVENT_ID(eventId, 100),
      CacheKeyPatterns.EVENT_HISTORY_LATEST_BY_EVENT_ID(eventId),
    ];

    await Promise.all(cacheKeysToDelete.map(key => this.cacheManager.del(key)));
  }

  private async invalidateEventHistoryCacheByDeviceId(deviceId: string): Promise<void> {
    const cacheKeysToDelete = [
      CacheKeyPatterns.EVENT_HISTORY_BY_DEVICE_ID(deviceId),
      CacheKeyPatterns.EVENT_HISTORY_BY_DEVICE_ID(deviceId, 10),
      CacheKeyPatterns.EVENT_HISTORY_BY_DEVICE_ID(deviceId, 50),
      CacheKeyPatterns.EVENT_HISTORY_BY_DEVICE_ID(deviceId, 100),
    ];

    await Promise.all(cacheKeysToDelete.map(key => this.cacheManager.del(key)));
  }
}
