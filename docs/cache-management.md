# Cache Management API

This document describes the cache management endpoints available in the CoDDN API.

## Overview

The cache management system provides endpoints to clear and monitor Redis cache entries used throughout the application. These endpoints are particularly useful for:

- Debugging cache-related issues
- Clearing stale cache data
- Monitoring cache performance
- Administrative maintenance

## Endpoints

### Clear All Cache

**POST** `/api/drone-authorizations/cache/clear`

Clears all cache entries across the application including:
- Drone authorization cache keys
- User cache keys
- Service zone cache keys
- Organization cache keys
- Legacy authorized drone cache keys
- HTTP cache interceptor keys
- Auth0 management token cache

**Response:**
```json
{
  "success": true,
  "message": "Successfully cleared 25 cache entries",
  "clearedKeys": ["key1", "key2", "..."]
}
```

### Clear Drone-Specific Cache

**POST** `/api/drone-authorizations/cache/clear/drone/:droneId`

Clears cache entries specific to a particular drone.

**Parameters:**
- `droneId` (path parameter): The ID of the drone to clear cache for

**Response:**
```json
{
  "success": true,
  "message": "Successfully cleared cache for drone 64f8b2c1e4b0a1234567890a"
}
```

### Get Cache Statistics

**GET** `/api/drone-authorizations/cache/stats`

Retrieves cache statistics and memory usage information.

**Response:**
```json
{
  "success": true,
  "stats": {
    "memory": "# Memory\r\nused_memory:1048576\r\nused_memory_human:1.00M\r\n...",
    "keyspace": "# Keyspace\r\ndb0:keys=150,expires=50,avg_ttl=180000\r\n...",
    "timestamp": "2024-01-15T10:30:00.000Z"
  },
  "message": "Cache statistics retrieved successfully"
}
```

## Cache Key Patterns

The system uses the following cache key patterns:

### Environment-Specific Keys
- Production: Uses base keys as-is
- Non-production: Prefixed with environment name (e.g., `dev:`, `staging:`)

### Drone Authorization Keys
- `drone:auth:{droneId}` - Authorization list for a specific drone
- `drone:auth:id:{authId}` - Specific authorization by ID
- `drone:auth:latest:{droneId}` - Latest authorization for a drone

### User Keys
- `user:sub:{sub}` - User data by Auth0 subject ID

### Service Zone Keys
- `servicezone:id:{id}` - Service zone by ID
- `servicezone:h3:{h3Indexes}` - Service zones by H3 indexes

### Organization Keys
- `org:auth0:{auth0Id}` - Organization by Auth0 ID

### Legacy Keys
- `authorized_drone:org_id:{orgId}:device_id:{deviceId}` - Legacy authorization cache

## Usage Examples

### Clear all cache (curl)
```bash
curl -X POST \
  http://localhost:3000/api/drone-authorizations/cache/clear \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Clear drone-specific cache
```bash
curl -X POST \
  http://localhost:3000/api/drone-authorizations/cache/clear/drone/64f8b2c1e4b0a1234567890a \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Get cache statistics
```bash
curl -X GET \
  http://localhost:3000/api/drone-authorizations/cache/stats \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Security Considerations

- All cache management endpoints require authentication
- Consider adding admin-only access controls for production environments
- Cache clearing operations are logged for audit purposes
- Statistics endpoint may expose sensitive information about system usage

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Failed to clear cache: Connection timeout"
}
```

Common error scenarios:
- Redis connection issues
- Permission denied
- Invalid drone ID format
- Cache store not available

## Performance Impact

- Cache clearing operations are generally fast but may cause temporary performance degradation
- Consider clearing cache during low-traffic periods
- Monitor application performance after cache clearing operations
- Cache statistics retrieval has minimal performance impact
