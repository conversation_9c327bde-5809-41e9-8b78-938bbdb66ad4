# Event Module Documentation

This document describes the comprehensive Event module with EventProfile and EventHistory services.

## Overview

The Event module provides a complete solution for managing drone detection events, including:

- **Event Profiles**: High-level event information and metadata
- **Event History**: Detailed tracking data and location history
- **Unified Event Service**: Combined operations across both profile and history
- **Caching**: Redis-based caching for improved performance
- **RESTful API**: Comprehensive endpoints for all event operations

## Architecture

### Services

1. **EventService** - Main service combining profile and history operations
2. **EventProfileService** - Manages event profile data with caching
3. **EventHistoryService** - Manages event history data with caching
4. **EventModel** - Legacy model (kept for backward compatibility)

### Data Models

#### EventProfile
- **EVENT_ID**: Unique event identifier
- **DEVICE_ID**: Device that detected the event
- **INCIDENT_ID**: Related incident identifier
- **ALERT_TIME**: When the alert was triggered
- **START_TIME/END_TIME**: Event duration
- **GPS coordinates**: Location data
- **STATS**: Calculated statistics (distance, velocity, etc.)
- **INFO**: OpenDroneID and radio information

#### EventHistory
- **EVENT_ID**: Links to EventProfile
- **DEVICE_ID**: Detecting device
- **LAT/LON**: Precise location coordinates
- **ALTITUDE/SPEED**: Flight parameters
- **TIME_STAMP**: Exact detection time
- **INFO**: Detailed radio and OpenDroneID data

## API Endpoints

### Legacy Endpoints (Backward Compatible)

#### Get Events by Alert Zone
**GET** `/api/events/alertZone/:alertZoneId`

Query Parameters:
- `page` (default: 1)
- `pageSize` (default: 10)
- `query` - Search term
- `startDate` - Filter start date
- `endDate` - Filter end date
- `sort` - Sort field
- `sortDirection` - Sort direction

#### Get Event Counts by Alert Zones
**GET** `/api/events/count/alertZone`

Query Parameters:
- `query` - Search term
- `startDate` - Filter start date
- `endDate` - Filter end date

#### Get Single Event with History
**GET** `/api/events/:id`

Query Parameters:
- `size` (default: 0) - Number of history records to include

### New Modular Endpoints

#### Event Profile Operations

**GET** `/api/events/profile/:eventId`
- Get event profile by ID

**GET** `/api/events/device/:deviceId/profiles`
- Get all event profiles for a device

**GET** `/api/events/range/profiles`
- Get event profiles by date range
- Query params: `startDate`, `endDate`, `skip`, `limit`

#### Event History Operations

**GET** `/api/events/history/:eventId`
- Get event history for an event
- Query params: `limit` (default: 50)

**GET** `/api/events/device/:deviceId/history`
- Get event history for a device
- Query params: `limit` (default: 100)

**GET** `/api/events/range/history`
- Get event history by date range
- Query params: `startDate`, `endDate`, `skip`, `limit`

**GET** `/api/events/latest/:eventId`
- Get latest history record for an event

#### Combined Operations

**GET** `/api/events/device/:deviceId`
- Get both profiles and history for a device

#### Location-Based Search

**GET** `/api/events/location/search`
- Search events by location
- Query params: `lat`, `lon`, `radius` (km, default: 1)

#### Statistics

**GET** `/api/events/stats/overview`
- Get comprehensive event statistics

**GET** `/api/events/stats/profiles`
- Get event profile statistics

**GET** `/api/events/stats/history`
- Get event history statistics
- Query params: `eventId`, `deviceId`

## Caching Strategy

### Cache Keys

#### Event Profile Cache
- `event:profile:id:{eventId}` - Individual event profile
- `event:profile:device:{deviceId}` - Event profiles by device

#### Event History Cache
- `event:history:event:{eventId}[:limit:{limit}]` - Event history by event ID
- `event:history:device:{deviceId}[:limit:{limit}]` - Event history by device ID
- `event:history:latest:{eventId}` - Latest history record

### Cache TTL
- **Event Profiles**: 5 minutes (300 seconds)
- **Event History**: 3 minutes (180 seconds) - shorter for real-time data

### Cache Invalidation
- Automatic invalidation on create/update/delete operations
- Admin endpoints for manual cache clearing
- Pattern-based cache clearing for bulk operations

## Usage Examples

### Get Event with History
```bash
curl -X GET \
  "http://localhost:3000/api/events/EVENT_123?size=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Search Events by Location
```bash
curl -X GET \
  "http://localhost:3000/api/events/location/search?lat=40.7128&lon=-74.0060&radius=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Get Device Event History
```bash
curl -X GET \
  "http://localhost:3000/api/events/device/DEVICE_456/history?limit=50" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Get Event Statistics
```bash
curl -X GET \
  "http://localhost:3000/api/events/stats/overview" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Performance Features

### Optimizations
- **MongoDB Indexing**: Optimized indexes on EVENT_ID, DEVICE_ID, TIME_STAMP
- **Redis Caching**: Intelligent caching with appropriate TTLs
- **Aggregation Pipelines**: Efficient database queries for statistics
- **Parallel Processing**: Concurrent operations where possible

### Monitoring
- Cache hit/miss statistics available via admin endpoints
- Performance metrics in system health endpoint
- Detailed logging for debugging

## Migration Notes

### Backward Compatibility
- All existing endpoints continue to work unchanged
- EventModel is preserved for legacy compatibility
- Database schema remains unchanged

### New Features
- Modular service architecture
- Comprehensive caching
- Enhanced API endpoints
- Location-based search
- Advanced statistics

## Error Handling

### Common Errors
- **404 Not Found**: Event or device not found
- **400 Bad Request**: Invalid parameters
- **500 Internal Server Error**: Database or cache issues

### Error Response Format
```json
{
  "statusCode": 404,
  "message": "Event profile with ID EVENT_123 not found",
  "error": "Not Found"
}
```

## Security Considerations

- All endpoints require authentication via JWT tokens
- Organization-based data isolation where applicable
- Input validation and sanitization
- Rate limiting recommended for high-frequency endpoints

## Future Enhancements

- Real-time event streaming via WebSockets
- Advanced analytics and machine learning integration
- Geospatial indexing for faster location queries
- Event correlation and pattern detection
- Export capabilities (CSV, JSON, etc.)
